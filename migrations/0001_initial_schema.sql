-- Initial schema for LettersBot database
-- This creates the basic tables needed for game persistence

-- Games table to store game sessions
CREATE TABLE IF NOT EXISTS games (
    id TEXT PRIMARY KEY,
    player_id TEXT,
    board_state TEXT NOT NULL, -- JSON representation of the board
    current_turn INTEGER DEFAULT 1,
    total_score INTEGER DEFAULT 0,
    moves_history TEXT, -- JSON array of moves
    game_status TEXT DEFAULT 'active', -- active, completed, abandoned
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Players table for basic player tracking
CREATE TABLE IF NOT EXISTS players (
    id TEXT PRIMARY KEY,
    username TEXT,
    email TEXT,
    total_games INTEGER DEFAULT 0,
    best_score INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Game statistics table
CREATE TABLE IF NOT EXISTS game_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    game_id TEXT NOT NULL,
    turn_number INTEGER NOT NULL,
    word TEXT NOT NULL,
    score INTEGER NOT NULL,
    tiles_used TEXT, -- JSON array of tile positions
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (game_id) REFERENCES games(id)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_games_player_id ON games(player_id);
CREATE INDEX IF NOT EXISTS idx_games_status ON games(game_status);
CREATE INDEX IF NOT EXISTS idx_game_stats_game_id ON game_stats(game_id);
CREATE INDEX IF NOT EXISTS idx_players_username ON players(username);
