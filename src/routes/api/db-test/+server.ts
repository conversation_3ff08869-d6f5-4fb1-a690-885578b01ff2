import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';

export const GET: RequestHandler = async ({ platform }) => {
	try {
		// Check if we have access to the D1 database
		if (!platform?.env?.DB) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'D1 database not available',
					message: 'Make sure you are running with <PERSON><PERSON><PERSON> and D1 is configured'
				}),
				{
					status: 500,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		// Test basic database connectivity
		const db = platform.env.DB;
		
		// Simple query to test connection
		const result = await db.prepare('SELECT 1 as test').first();
		
		// Test if our tables exist
		const tablesQuery = await db.prepare(`
			SELECT name FROM sqlite_master 
			WHERE type='table' AND name IN ('games', 'players', 'game_stats')
		`).all();

		return new Response(
			JSON.stringify({
				success: true,
				message: 'D1 database connection successful',
				data: {
					connectionTest: result,
					tablesFound: tablesQuery.results?.map((row: any) => row.name) || [],
					timestamp: new Date().toISOString()
				}
			}),
			{
				status: 200,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	} catch (error) {
		console.error('Database test error:', error);
		
		return new Response(
			JSON.stringify({
				success: false,
				error: 'Database test failed',
				message: error instanceof Error ? error.message : 'Unknown error',
				timestamp: new Date().toISOString()
			}),
			{
				status: 500,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	}
};
